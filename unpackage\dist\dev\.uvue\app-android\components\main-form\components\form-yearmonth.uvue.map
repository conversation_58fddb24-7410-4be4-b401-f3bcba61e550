{"version": 3, "sources": ["components/main-form/components/form-yearmonth.uvue"], "names": [], "mappings": "AAiBC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,eAAe;IACrB,KAAK,EAAE,CAAC,QAAQ,CAAC;IACjB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,IAAG,IAAK,GAAE,IAAK,QAAQ,CAAC,aAAa,CAAA;SAC3C;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,EAAC,IAAK,MAAM;YACxB,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,WAAW,EAAE,OAAO;YACpB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAE;YAChB,WAAW,EAAE,EAAC,IAAK,MAAM;YACzB,SAAS,EAAE,SAAQ,IAAK,MAAM;YAC9B,OAAO,EAAE,SAAQ,IAAK,MAAK;SAC5B,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,mBAAkB;gBAClB,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,IAAK,MAAK,CAAA;gBACnC,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;oBACzB,IAAI,CAAC,iBAAiB,EAAC,CAAA;iBACxB;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,IAAK,MAAK,CAAA;YAE1C,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAE1C,SAAQ;YACR,IAAI,CAAC,iBAAiB,EAAC,CAAA;YAEvB,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAC;QAED,SAAQ;QACR,iBAAiB,IAAI,IAAG;YACvB,IAAI,IAAI,CAAC,UAAS,IAAK,EAAC,IAAK,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBACrD,kBAAiB;gBACjB,MAAM,gBAAe,GAAI,eAAc,CAAA;gBACvC,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBAC3C,MAAM,KAAI,GAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAA,CAAA;oBACvC,MAAM,IAAG,GAAI,KAAK,CAAC,CAAC,CAAA,CAAA;oBACpB,MAAM,KAAI,GAAI,KAAK,CAAC,CAAC,CAAA,CAAA;oBACrB,IAAI,CAAC,WAAU,GAAI,GAAG,IAAI,IAAI,KAAK,GAAE,CAAA;oBACrC,mDAAkD;oBAClD,IAAI,CAAC,WAAU,GAAI,GAAG,IAAI,CAAC,UAAU,KAAI,CAAA;iBAC1C;qBAAO;oBACN,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,UAAS,CAAA;oBACjC,IAAI,CAAC,WAAU,GAAI,EAAC,CAAA;iBACrB;aACD;iBAAO;gBACN,IAAI,CAAC,WAAU,GAAI,OAAM,CAAA;gBACzB,IAAI,CAAC,WAAU,GAAI,EAAC,CAAA;aACrB;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,UAAS,GAAI,GAAG,CAAC,IAAG,CAAA;wBAC1B,IAAG,OAAO,UAAS,KAAM,QAAQ,EAAC;4BACjC,IAAI,CAAC,UAAS,GAAI,UAAS,IAAK,MAAK,CAAA;4BACrC,IAAI,CAAC,iBAAiB,EAAC,CAAA;4BACvB,MAAM,MAAM,EAAE,eAAc,GAAI;gCAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gCACjB,KAAK,EAAE,UAAS;6BACjB,CAAA;4BACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;yBACnB;oBAED,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAK,IAAK,OAAO,IAAI,CAAC,UAAS,KAAK,QAAQ,EAAE;gBACtD,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,IAAI,CAAC,UAAS;iBACpB,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,QAAO;YACP,IAAI,IAAI,CAAC,UAAS,IAAK,EAAC,IAAK,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBACrD,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,OAAM,CAAA;gBAC1B,OAAO,KAAI,CAAA;aACZ;YAEA,iBAAgB;YAChB,MAAM,gBAAe,GAAI,eAAc,CAAA;YACvC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC5C,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,SAAQ,CAAA;gBAC5B,OAAO,KAAI,CAAA;aACZ;YAEA,SAAQ;YACR,MAAM,KAAI,GAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAA,CAAA;YACvC,MAAM,KAAI,GAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA,CAAA;YAC/B,IAAI,KAAI,GAAI,CAAA,IAAK,KAAI,GAAI,EAAE,EAAE;gBAC5B,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,aAAY,CAAA;gBAChC,OAAO,KAAI,CAAA;aACZ;YAEA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,IAAK,MAAK,CAAA;YACtC,SAAQ;YACR,IAAI,CAAC,iBAAiB,EAAC,CAAA;YACvB,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAC;QAED,YAAW;QACX,YAAY,CAAC,KAAK,EAAE,oBAAoB,GAAG,IAAG;YAC7C,MAAM,YAAW,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,CAAA;YACtC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,YAAY,EAAA,6DAAA,CAAA,CAAA;YAC1C,qCAAoC;YACpC,oDAAmD;YACnD,iEAAgE;YAEhE,qCAAoC;YACpC,uBAAsB;YACtB,qBAAoB;YACpB,KAAI;YACJ,uBAAsB;YACtB,IAAG;QACJ,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;WAxNA,GAAA,CAYiB,yBAAA,EAAA,GAAA,CAAA;QAZA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IAOS,GAAA,EAAA,CAAA,EAAA,CAAA;YAPT,GAAA,CAOS,iBAAA,EAAA,GAAA,CAAA;gBAPD,KAAK,EAAC,kBAAkB;gBAAC,IAAI,EAAC,MAAM;gBAAC,MAAM,EAAC,OAAO;gBAAE,KAAK,EAAE,IAAA,CAAA,WAAW;gBAAG,KAAK,EAAE,IAAA,CAAA,SAAS;gBAAG,GAAG,EAAE,IAAA,CAAA,OAAO;gBAAG,QAAM,EAAE,IAAA,CAAA,YAAY;;qCACvI,IAKO,GAAA,EAAA,CAAA,EAAA,CAAA;oBALP,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA,EALD,KAAK,EAAC,6BAA6B,EAAA,CAAA,EAAA;wBACxC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;4BAC3B,GAAA,CAA2C,MAAA,EAAA,GAAA,CAAA,EAArC,KAAK,EAAC,qBAAqB,EAAA,CAAA,EAAC,IAAE,CAAA;;wBAErC,GAAA,CAAqD,MAAA,EAAA,GAAA,CAAA,EAA/C,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,WAAW,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA", "file": "components/main-form/components/form-yearmonth.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<picker class=\"yearmonth-picker\" mode=\"date\" fields=\"month\" :value=\"pickerValue\" :start=\"startDate\" :end=\"endDate\" @change=\"onDateChange\">\n\t\t\t\t<view class=\"yearmonth-display-container\">\n\t\t\t\t\t<view class=\"yearmonth-icon\">\n\t\t\t\t\t\t<text class=\"yearmonth-icon-text\">📅</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"yearmonth-text\">{{ displayText }}</text>\n\t\t\t\t</view>\n\t\t\t</picker>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\texport default {\n\t\tname: \"FormYearmonth\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\" as string,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tdisplayText: \"请选择年月\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\",\n\t\t\t\tpickerValue: \"\" as string,\n\t\t\t\tstartDate: \"1900-01\" as string,\n\t\t\t\tendDate: \"2100-12\" as string\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateDisplayText()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateDisplayText()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新显示文本\n\t\t\tupdateDisplayText(): void {\n\t\t\t\tif (this.fieldValue != \"\" && this.fieldValue != null) {\n\t\t\t\t\t// 验证格式是否为 YYYY-MM\n\t\t\t\t\tconst yearMonthPattern = /^\\d{4}-\\d{2}$/\n\t\t\t\t\tif (yearMonthPattern.test(this.fieldValue)) {\n\t\t\t\t\t\tconst parts = this.fieldValue.split(\"-\")\n\t\t\t\t\t\tconst year = parts[0]\n\t\t\t\t\t\tconst month = parts[1]\n\t\t\t\t\t\tthis.displayText = `${year}年${month}月`\n\t\t\t\t\t\t// 更新picker的值，picker需要完整的日期格式YYYY-MM-DD，这里设置为月份的第一天\n\t\t\t\t\t\tthis.pickerValue = `${this.fieldValue}-01`\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.displayText = this.fieldValue\n\t\t\t\t\t\tthis.pickerValue = \"\"\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.displayText = \"请选择年月\"\n\t\t\t\t\tthis.pickerValue = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\n\t\t\t\t\t\t\tif(typeof save_value === 'string'){\n\t\t\t\t\t\t\t\tthat.fieldValue = save_value as string\n\t\t\t\t\t\t\t\tthat.updateDisplayText()\n\t\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && typeof this.fieldValue ===\"string\") {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 年月值验证\n\t\t\t\tif (this.fieldValue == \"\" || this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择年月\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 验证年月格式 YYYY-MM\n\t\t\t\tconst yearMonthPattern = /^\\d{4}-\\d{2}$/\n\t\t\t\tif (!yearMonthPattern.test(this.fieldValue)) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"年月格式不正确\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 验证月份范围\n\t\t\t\tconst parts = this.fieldValue.split(\"-\")\n\t\t\t\tconst month = parseInt(parts[1])\n\t\t\t\tif (month < 1 || month > 12) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"月份必须在1-12之间\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateDisplayText()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 日期选择器变化事件\n\t\t\tonDateChange(event: UniPickerChangeEvent): void {\n\t\t\t\tconst selectedDate = event.detail.value\n\t\t\t\tconsole.log(\"Selected date:\", selectedDate)\n\t\t\t\t// // 从完整日期格式YYYY-MM-DD中提取年月部分YYYY-MM\n\t\t\t\t// if (selectedDate != \"\" && selectedDate != null) {\n\t\t\t\t// \tconst yearMonth = selectedDate.substring(0, 7) // 提取YYYY-MM部分\n\n\t\t\t\t// \tconst result: FormChangeEvent = {\n\t\t\t\t// \t\tindex: this.index,\n\t\t\t\t// \t\tvalue: yearMonth\n\t\t\t\t// \t}\n\t\t\t\t// \tthis.change(result)\n\t\t\t\t// }\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.yearmonth-picker {\n\t\tflex: 1;\n\t}\n\n\t.yearmonth-display-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.yearmonth-icon {\n\t\twidth: 60rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.yearmonth-icon-text {\n\t\tfont-size: 32rpx;\n\t}\n\n\t.yearmonth-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n</style>"]}