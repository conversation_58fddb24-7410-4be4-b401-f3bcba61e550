@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
open class GenComponentsMainFormToolsMainYearmonthPicker : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun() {
            this.initializeData()
        }
        , __ins)
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        return if (isTrue(_ctx.visible)) {
            _cE("view", _uM("key" to 0, "class" to "picker-overlay", "onClick" to _ctx.onOverlayClick), _uA(
                _cE("view", _uM("class" to "picker-modal", "onClick" to withModifiers(fun(){}, _uA(
                    "stop"
                ))), _uA(
                    _cE("view", _uM("class" to "yearmonth-picker-container"), _uA(
                        _cE("view", _uM("class" to "navbar"), _uA(
                            _cE("text", _uM("class" to "nav-btn cancel-btn", "onClick" to _ctx.onCancel), "取消", 8, _uA(
                                "onClick"
                            )),
                            _cE("text", _uM("class" to "nav-title"), "选择年月"),
                            _cE("view", _uM("class" to "confirm-btn-container"), _uA(
                                _cE("text", _uM("class" to "nav-btn confirm-btn", "onClick" to _ctx.onConfirm), "确定", 8, _uA(
                                    "onClick"
                                ))
                            ))
                        )),
                        _cE("view", _uM("class" to "picker-body"), _uA(
                            _cE("view", _uM("class" to "picker-section"), _uA(
                                _cE("text", _uM("class" to "section-title"), "年份"),
                                _cE("scroll-view", _uM("direction" to "vertical", "class" to "year-scroll", "scroll-top" to _ctx.yearScrollTop), _uA(
                                    _cE("view", _uM("class" to "year-list"), _uA(
                                        _cE(Fragment, null, RenderHelpers.renderList(_ctx.yearList, fun(year, __key, __index, _cached): Any {
                                            return _cE("view", _uM("key" to year, "class" to _nC(_uA(
                                                "year-item",
                                                _uM("year-active" to (year == _ctx.selectedYear))
                                            )), "onClick" to fun(){
                                                _ctx.onYearSelect(year)
                                            }), _uA(
                                                _cE("text", _uM("class" to "year-text"), _tD(year), 1)
                                            ), 10, _uA(
                                                "onClick"
                                            ))
                                        }), 128)
                                    ))
                                ), 8, _uA(
                                    "scroll-top"
                                ))
                            )),
                            _cE("view", _uM("class" to "picker-section picker-section-last"), _uA(
                                _cE("text", _uM("class" to "section-title"), "月份"),
                                _cE("scroll-view", _uM("direction" to "vertical", "class" to "month-scroll"), _uA(
                                    _cE("view", _uM("class" to "month-grid"), _uA(
                                        _cE(Fragment, null, RenderHelpers.renderList(_ctx.monthList, fun(month, __key, __index, _cached): Any {
                                            return _cE("view", _uM("key" to month, "class" to _nC(_uA(
                                                "month-item",
                                                _uM("month-active" to (month == _ctx.selectedMonth))
                                            )), "onClick" to fun(){
                                                _ctx.onMonthSelect(month)
                                            }), _uA(
                                                _cE("text", _uM("class" to "month-text"), _tD(month) + "月", 1)
                                            ), 10, _uA(
                                                "onClick"
                                            ))
                                        }), 128)
                                    ))
                                ))
                            ))
                        )),
                        _cE("view", _uM("class" to "current-selection"), _uA(
                            _cE("text", _uM("class" to "selection-label"), "当前选择："),
                            _cE("text", _uM("class" to "selection-value"), _tD(_ctx.selectedYear) + "年" + _tD(_ctx.selectedMonth) + "月", 1)
                        ))
                    ))
                ), 8, _uA(
                    "onClick"
                ))
            ), 8, _uA(
                "onClick"
            ))
        } else {
            _cC("v-if", true)
        }
    }
    open var initialYear: Number by `$props`
    open var initialMonth: Number by `$props`
    open var before: Number by `$props`
    open var after: Number by `$props`
    open var visible: Boolean by `$data`
    open var selectedYear: Number by `$data`
    open var selectedMonth: Number by `$data`
    open var yearList: UTSArray<Number> by `$data`
    open var monthList: UTSArray<Number> by `$data`
    open var yearScrollTop: Number by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("visible" to false as Boolean, "selectedYear" to Date().getFullYear() as Number, "selectedMonth" to (Date().getMonth() + 1) as Number, "yearList" to _uA<Number>(), "monthList" to _uA<Number>(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12), "yearScrollTop" to 0 as Number)
    }
    open var initializeData = ::gen_initializeData_fn
    open fun gen_initializeData_fn() {
        this.selectedYear = this.initialYear
        this.selectedMonth = this.initialMonth
        this.generateYearList()
    }
    open var generateYearList = ::gen_generateYearList_fn
    open fun gen_generateYearList_fn() {
        val currentYear: Number = Date().getFullYear()
        val startYear: Number = currentYear - this.before
        val endYear: Number = currentYear + this.after
        this.yearList = _uA()
        run {
            var year = startYear
            while(year <= endYear){
                this.yearList.push(year)
                year++
            }
        }
    }
    open var onYearSelect = ::gen_onYearSelect_fn
    open fun gen_onYearSelect_fn(year: Number) {
        this.selectedYear = year
    }
    open var onMonthSelect = ::gen_onMonthSelect_fn
    open fun gen_onMonthSelect_fn(month: Number) {
        this.selectedMonth = month
    }
    open var open = ::gen_open_fn
    open fun gen_open_fn() {
        this.visible = true
        this.calculateYearScrollPosition()
    }
    open var close = ::gen_close_fn
    open fun gen_close_fn() {
        this.visible = false
    }
    open var calculateYearScrollPosition = ::gen_calculateYearScrollPosition_fn
    open fun gen_calculateYearScrollPosition_fn() {
        this.`$nextTick`(fun(){
            val selectedIndex: Number = this.yearList.indexOf(this.selectedYear)
            if (selectedIndex != -1) {
                this.yearScrollTop = Math.max(0, (selectedIndex - 2) * 30)
            }
        }
        )
    }
    open var onOverlayClick = ::gen_onOverlayClick_fn
    open fun gen_onOverlayClick_fn() {
        this.close()
        this.`$emit`("cancel")
    }
    open var onCancel = ::gen_onCancel_fn
    open fun gen_onCancel_fn() {
        this.close()
        this.`$emit`("cancel")
    }
    open var onConfirm = ::gen_onConfirm_fn
    open fun gen_onConfirm_fn() {
        this.close()
        val result = YearMonthData(year = this.selectedYear, month = this.selectedMonth)
        this.`$emit`("confirm", result)
    }
    companion object {
        var name = "main-yearmonth-picker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("picker-overlay" to _pS(_uM("position" to "fixed", "top" to 0, "left" to 0, "right" to 0, "bottom" to 0, "backgroundColor" to "rgba(0,0,0,0.5)", "display" to "flex", "alignItems" to "center", "justifyContent" to "center", "zIndex" to 1000)), "picker-modal" to _pS(_uM("width" to "90%", "maxWidth" to "600rpx", "backgroundColor" to "#ffffff", "borderTopLeftRadius" to "20rpx", "borderTopRightRadius" to "20rpx", "borderBottomRightRadius" to "20rpx", "borderBottomLeftRadius" to "20rpx", "overflow" to "hidden", "boxShadow" to "0 8px 32px rgba(0, 0, 0, 0.3)")), "yearmonth-picker-container" to _pS(_uM("width" to "100%", "backgroundColor" to "#ffffff", "display" to "flex", "flexDirection" to "column")), "navbar" to _pS(_uM("height" to 44, "backgroundColor" to "#f8f8f8", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#e5e5e5", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "space-between", "paddingTop" to 0, "paddingRight" to 10, "paddingBottom" to 0, "paddingLeft" to 10)), "nav-btn" to _pS(_uM("fontSize" to 16, "color" to "#007aff", "paddingTop" to 8, "paddingRight" to 12, "paddingBottom" to 8, "paddingLeft" to 12)), "cancel-btn" to _pS(_uM("color" to "#999999")), "confirm-btn-container" to _pS(_uM("height" to 30, "backgroundColor" to "#007aff", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "boxShadow" to "0 2rpx 8rpx rgba(0, 122, 255, 0.3)")), "confirm-btn" to _pS(_uM("color" to "#ffffff", "fontWeight" to "bold")), "nav-title" to _pS(_uM("fontSize" to 17, "color" to "#333333")), "picker-body" to _pS(_uM("display" to "flex", "flexDirection" to "row", "height" to "400rpx", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#f0f0f0")), "picker-section" to _pS(_uM("flex" to 1, "display" to "flex", "flexDirection" to "column", "borderRightWidth" to 1, "borderRightStyle" to "solid", "borderRightColor" to "#f0f0f0")), "picker-section-last" to _pS(_uM("flex" to 2, "borderRightWidth" to "medium", "borderRightStyle" to "none", "borderRightColor" to "#000000")), "section-title" to _pS(_uM("textAlign" to "center", "paddingTop" to "20rpx", "paddingRight" to 0, "paddingBottom" to "20rpx", "paddingLeft" to 0, "fontSize" to "28rpx", "fontWeight" to "bold", "color" to "#333333", "backgroundColor" to "#f8f9fa", "borderBottomWidth" to 1, "borderBottomStyle" to "solid", "borderBottomColor" to "#f0f0f0")), "year-scroll" to _pS(_uM("flex" to 1, "height" to "340rpx")), "year-list" to _pS(_uM("paddingTop" to "10rpx", "paddingRight" to 0, "paddingBottom" to "10rpx", "paddingLeft" to 0)), "year-item" to _uM("" to _uM("height" to "60rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "marginTop" to 0, "marginRight" to "15rpx", "marginBottom" to "8rpx", "marginLeft" to "15rpx", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx"), ".year-active" to _uM("backgroundColor" to "#007aff", "transform" to "scale(1.05)")), "year-text" to _uM("" to _uM("fontSize" to "28rpx", "color" to "#666666"), ".year-active " to _uM("color" to "#ffffff", "fontWeight" to "bold")), "month-scroll" to _pS(_uM("flex" to 1, "height" to "340rpx")), "month-grid" to _pS(_uM("display" to "flex", "flexDirection" to "row", "flexWrap" to "wrap", "paddingTop" to "20rpx", "paddingRight" to "15rpx", "paddingBottom" to "20rpx", "paddingLeft" to "15rpx", "justifyContent" to "space-between", "alignContent" to "flex-start", "flex" to 1)), "month-item" to _uM("" to _uM("width" to "100rpx", "height" to "60rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "backgroundColor" to "#f8f9fa", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "marginBottom" to "12rpx"), ".month-active" to _uM("backgroundColor" to "#007aff", "transform" to "scale(1.05)")), "month-text" to _uM("" to _uM("fontSize" to "24rpx", "color" to "#666666"), ".month-item.month-active " to _uM("color" to "#ffffff", "fontWeight" to "bold")), "current-selection" to _pS(_uM("paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx", "backgroundColor" to "#f8f9fa", "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "justifyContent" to "center")), "selection-label" to _pS(_uM("fontSize" to "28rpx", "color" to "#666666", "marginRight" to "10rpx")), "selection-value" to _pS(_uM("fontSize" to "32rpx", "color" to "#007aff", "fontWeight" to "bold")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("cancel" to null, "confirm" to null)
        var props = _nP(_uM("initialYear" to _uM("type" to "Number", "default" to fun(): Number {
            return Date().getFullYear()
        }
        ), "initialMonth" to _uM("type" to "Number", "default" to fun(): Number {
            return Date().getMonth() + 1
        }
        ), "before" to _uM("type" to "Number", "default" to fun(): Number {
            return 50
        }
        ), "after" to _uM("type" to "Number", "default" to fun(): Number {
            return 10
        }
        )))
        var propsNeedCastKeys = _uA(
            "initialYear",
            "initialMonth",
            "before",
            "after"
        )
        var components: Map<String, CreateVueComponent> = _uM()
    }
}
