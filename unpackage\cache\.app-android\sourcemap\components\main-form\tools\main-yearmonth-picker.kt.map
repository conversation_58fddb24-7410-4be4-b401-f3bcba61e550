{"version": 3, "sources": ["components/main-form/tools/main-yearmonth-picker.uvue"], "sourcesContent": ["<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"yearmonth-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">选择年月</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 年月选择区域 -->\r\n\t\t\t\t<view class=\"picker-body\">\r\n\t\t\t\t\t<!-- 年份选择区域 -->\r\n\t\t\t\t\t<view class=\"picker-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">年份</text>\r\n\r\n\t\t\t\t\t\t<scroll-view direction=\"vertical\" class=\"year-scroll\" :scroll-top=\"yearScrollTop\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"year-list\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"year in yearList\" :key=\"year\" class=\"year-item\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'year-active': year == selectedYear }\" @click=\"onYearSelect(year)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"year-text\" :class=\"{ 'year-text-active': year == selectedYear }\">{{ year }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 月份选择区域 -->\r\n\t\t\t\t\t<view class=\"picker-section picker-section-last\">\r\n\t\t\t\t\t\t<text class=\"section-title\">月份</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t<scroll-view direction=\"vertical\" class=\"month-scroll\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"month-grid\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"month in monthList\" :key=\"month\" class=\"month-item\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'month-active': month == selectedMonth }\" @click=\"onMonthSelect(month)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"month-text\">{{ month }}月</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 当前选择显示区域 -->\r\n\t\t\t\t<view class=\"current-selection\">\r\n\t\t\t\t\t<text class=\"selection-label\">当前选择：</text>\r\n\t\t\t\t\t<text class=\"selection-value\">{{ selectedYear }}年{{ selectedMonth }}月</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义年月选择器的数据类型\r\n\ttype YearMonthData = {\r\n\t\tyear : number,\r\n\t\tmonth : number\r\n\t}\r\n\r\n\ttype YearRange = {\r\n\t\tbefore : number,\r\n\t\tafter : number\r\n\t}\r\n\r\n\texport default {\r\n\t\tname: \"main-yearmonth-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tprops: {\r\n\t\t\t// 初始年份\r\n\t\t\tinitialYear: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => new Date().getFullYear()\r\n\t\t\t},\r\n\t\t\t// 初始月份\r\n\t\t\tinitialMonth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => new Date().getMonth() + 1\r\n\t\t\t},\r\n\t\t\tbefore: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => 50\r\n\t\t\t},\r\n\t\t\tafter: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => 10\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的年份\r\n\t\t\t\tselectedYear: new Date().getFullYear() as number,\r\n\t\t\t\t// 当前选中的月份\r\n\t\t\t\tselectedMonth: (new Date().getMonth() + 1) as number,\r\n\t\t\t\t// 年份列表\r\n\t\t\t\tyearList: [] as number[],\r\n\t\t\t\t// 月份列表\r\n\t\t\t\tmonthList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as number[],\r\n\t\t\t\t// 年份滚动位置\r\n\t\t\t\tyearScrollTop: 0 as number\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.initializeData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tinitializeData() {\r\n\t\t\t\tthis.selectedYear = this.initialYear\r\n\t\t\t\tthis.selectedMonth = this.initialMonth\r\n\t\t\t\tthis.generateYearList()\r\n\t\t\t},\r\n\r\n\t\t\t// 生成年份列表\r\n\t\t\tgenerateYearList() {\r\n\t\t\t\tconst currentYear : number = new Date().getFullYear()\r\n\r\n\r\n\t\t\t\tconst startYear : number = currentYear - this.before\r\n\t\t\t\tconst endYear : number = currentYear + this.after\r\n\r\n\t\t\t\tthis.yearList = []\r\n\t\t\t\tfor (let year = startYear; year <= endYear; year++) {\r\n\t\t\t\t\tthis.yearList.push(year)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 年份选择事件\r\n\t\t\tonYearSelect(year : number) {\r\n\t\t\t\tthis.selectedYear = year\r\n\t\t\t},\r\n\r\n\t\t\t// 月份选择事件\r\n\t\t\tonMonthSelect(month : number) {\r\n\t\t\t\tthis.selectedMonth = month\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t\tthis.calculateYearScrollPosition()\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 计算年份滚动位置\r\n\t\t\tcalculateYearScrollPosition() {\r\n\t\t\t\t// 在下一个tick中计算滚动位置，确保DOM已更新\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst selectedIndex : number = this.yearList.indexOf(this.selectedYear)\r\n\t\t\t\t\tif (selectedIndex != -1) {\r\n\t\t\t\t\t\t// 每个年份项高度约为60rpx，让选中年份显示在中间位置\r\n\t\t\t\t\t\tthis.yearScrollTop = Math.max(0, (selectedIndex - 2) * 30)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst result : YearMonthData = {\r\n\t\t\t\t\tyear: this.selectedYear,\r\n\t\t\t\t\tmonth: this.selectedMonth\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('confirm', result)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 90%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.yearmonth-picker-container {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 选择器主体区域 */\r\n\t.picker-body {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\theight: 400rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.picker-section {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tborder-right: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.picker-section-last {\r\n\t\tflex:2;\r\n\t\tborder-right: none;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t/* 年份选择区域 */\r\n\t.year-scroll {\r\n\t\tflex: 1;\r\n\t\theight: 340rpx;\r\n\t}\r\n\r\n\t.year-list {\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.year-item {\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin: 0 15rpx 8rpx 15rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.year-item.year-active {\r\n\t\tbackground-color: #007aff;\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.year-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.year-text-active {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 月份选择区域 */\r\n\t.month-scroll {\r\n\t\tflex: 1;\r\n\t\theight: 340rpx;\r\n\t}\r\n\r\n\t.month-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 20rpx 15rpx;\r\n\t\tjustify-content: space-between;\r\n\t\talign-content: flex-start;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.month-item {\r\n\t\twidth: 100rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.month-item.month-active {\r\n\t\tbackground-color: #007aff;\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.month-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.month-item.month-active .month-text {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 当前选择显示区域 */\r\n\t.current-selection {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.selection-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.selection-value {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #007aff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>"], "names": [], "mappings": ";;;;;;;;;;;;;AAuEM;;kBAwCJ,MAAO;YACN,IAAI,CAAC,cAAc;QACpB;;;;;;;0BA/GW,KAAA,OAAO;YAAnB,IAsDO,QAAA,gBAtDc,WAAM,kBAAkB,aAAO,KAAA,cAAc;gBACjE,IAoDO,QAAA,IApDD,WAAM,gBAAgB,aAAK,cAAN,KAAA,CAAA,GAAc;oBAAA;iBAAA;oBACxC,IAkDO,QAAA,IAlDD,WAAM,+BAA4B;wBAEvC,IAMO,QAAA,IAND,WAAM,WAAQ;4BACnB,IAA4D,QAAA,IAAtD,WAAM,sBAAsB,aAAO,KAAA,QAAQ,GAAE,MAAE,CAAA,EAAA;gCAAA;6BAAA;4BACrD,IAAmC,QAAA,IAA7B,WAAM,cAAY;4BACxB,IAEO,QAAA,IAFD,WAAM,0BAAuB;gCAClC,IAA8D,QAAA,IAAxD,WAAM,uBAAuB,aAAO,KAAA,SAAS,GAAE,MAAE,CAAA,EAAA;oCAAA;iCAAA;;;wBAKzD,IAgCO,QAAA,IAhCD,WAAM,gBAAa;4BAExB,IAaO,QAAA,IAbD,WAAM,mBAAgB;gCAC3B,IAAqC,QAAA,IAA/B,WAAM,kBAAgB;gCAE5B,IASc,eAAA,IATD,eAAU,YAAW,WAAM,eAAe,gBAAY,KAAA,aAAa;oCAE/E,IAKO,QAAA,IALD,WAAM,cAAW;wCACtB,IAGO,UAAA,IAAA,EAAA,cAAA,UAAA,CAHc,KAAA,QAAQ,EAAA,IAAhB,MAAA,OAAA,SAAI,UAAA,GAAA,CAAA;mDAAjB,IAGO,QAAA,IAHyB,SAAK,MAAM,WAAK,IAAA;gDAAC;gDACxC,IAAA,kBAAA,QAAA,KAAA,YAAA;6CAAuC,GAAG,aAAK,KAAA;gDAAE,KAAA,YAAY,CAAC;4CAAI;gDAC1E,IAA+F,QAAA,IAAzF,WAAK,IAAA;oDAAC;oDAAoB,IAAA,uBAAA,QAAA,KAAA,YAAA;iDAA4C,QAAK,OAAI,CAAA;;;;;;;;;;4BAQzF,IAaO,QAAA,IAbD,WAAM,uCAAoC;gCAC/C,IAAqC,QAAA,IAA/B,WAAM,kBAAgB;gCAE5B,IASc,eAAA,IATD,eAAU,YAAW,WAAM;oCAEvC,IAKO,QAAA,IALD,WAAM,eAAY;wCACvB,IAGO,UAAA,IAAA,EAAA,cAAA,UAAA,CAHe,KAAA,SAAS,EAAA,IAAlB,OAAA,OAAA,SAAK,UAAA,GAAA,CAAA;mDAAlB,IAGO,QAAA,IAH2B,SAAK,OAAO,WAAK,IAAA;gDAAC;gDAC3C,IAAA,mBAAA,SAAA,KAAA,aAAA;6CAA0C,GAAG,aAAK,KAAA;gDAAE,KAAA,aAAa,CAAC;4CAAK;gDAC/E,IAA4C,QAAA,IAAtC,WAAM,eAAY,IAAI,SAAQ,KAAC,CAAA;;;;;;;;;wBAS1C,IAGO,QAAA,IAHD,WAAM,sBAAmB;4BAC9B,IAA0C,QAAA,IAApC,WAAM,oBAAkB;4BAC9B,IAA4E,QAAA,IAAtE,WAAM,oBAAiB,IAAI,KAAA,YAAY,IAAG,MAAC,IAAG,KAAA,aAAa,IAAG,KAAC,CAAA;;;;;;;;;;;;;;;;;aA8CtE,SAAkB,OAAO;aAEzB,cAA0C,MAAM;aAEhD,eAA8C,MAAM;aAEpD,mBAAgB,MAAM;aAEtB,oBAAsD,MAAM;aAE5D,eAAoB,MAAK;;;mBAVzB,aAAS,KAAI,CAAA,EAAA,CAAK,OAAO,EAEzB,kBAAc,AAAI,OAAO,WAAW,GAAC,EAAA,CAAK,MAAM,EAEhD,mBAAe,CAAC,AAAI,OAAO,QAAQ,KAAK,CAAC,EAAA,EAAA,CAAK,MAAM,EAEpD,cAAU,IAAM,MAAM,KAEtB,eAAW,IAA2C,MAAM,EAAhD,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAEjD,mBAAe,CAAA,CAAA,EAAA,CAAK,MAAK;;aAQ1B;aAAA,wBAAc;QACb,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,WAAU;QACnC,IAAI,CAAC,aAAY,GAAI,IAAI,CAAC,YAAW;QACrC,IAAI,CAAC,gBAAgB;IACtB;aAGA;aAAA,0BAAgB;QACf,IAAM,aAAc,MAAK,GAAI,AAAI,OAAO,WAAW;QAGnD,IAAM,WAAY,MAAK,GAAI,cAAc,IAAI,CAAC,MAAK;QACnD,IAAM,SAAU,MAAK,GAAI,cAAc,IAAI,CAAC,KAAI;QAEhD,IAAI,CAAC,QAAO,GAAI,KAAC;YACjB;YAAK,IAAI,OAAO;YAAhB,MAA2B,QAAQ;gBAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBADwB;;;IAG7C;aAGA;aAAA,oBAAa,MAAO,MAAM,EAAA;QACzB,IAAI,CAAC,YAAW,GAAI;IACrB;aAGA;aAAA,qBAAc,OAAQ,MAAM,EAAA;QAC3B,IAAI,CAAC,aAAY,GAAI;IACtB;aAGA;aAAA,cAAI;QACH,IAAI,CAAC,OAAM,GAAI,IAAG;QAClB,IAAI,CAAC,2BAA2B;IACjC;aAGA;aAAA,eAAK;QACJ,IAAI,CAAC,OAAM,GAAI,KAAI;IACpB;aAGA;aAAA,qCAA2B;QAE1B,IAAI,CAAC,WAAS,CAAC,KAAI;YAClB,IAAM,eAAgB,MAAK,GAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY;YACtE,IAAI,iBAAiB,CAAC,CAAC,EAAE;gBAExB,IAAI,CAAC,aAAY,GAAI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,EAAE;;QAE3D;;IACD;aAGA;aAAA,wBAAc;QACb,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,OAAK,CAAC;IACZ;aAGA;aAAA,kBAAQ;QACP,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,OAAK,CAAC;IACZ;aAGA;aAAA,mBAAS;QACR,IAAI,CAAC,KAAK;QACV,IAAM,uBACL,OAAM,IAAI,CAAC,YAAY,EACvB,QAAO,IAAI,CAAC,aAAY;QAEzB,IAAI,CAAC,OAAK,CAAC,WAAW;IACvB;;mBArHK;;;;;;;;;;;;;kFAMK,OAAA,MAAA;mBAAM,AAAI,OAAO,WAAW;;kEAK5B,OAAA,MAAA;mBAAM,AAAI,OAAO,QAAQ,KAAK,CAAA;;4DAI9B,OAAA,MAAA;mBAAM,EAAC;;2DAIP,OAAA,MAAA;mBAAM,EAAC;;;;;;;;;;;AAoGnB"}