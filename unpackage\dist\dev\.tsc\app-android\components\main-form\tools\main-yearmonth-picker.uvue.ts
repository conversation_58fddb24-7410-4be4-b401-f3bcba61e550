
	// 定义年月选择器的数据类型
	type YearMonthData = { __$originalPosition?: UTSSourceMapPosition<"YearMonthData", "components/main-form/tools/main-yearmonth-picker.uvue", 62, 7>;
		year : number,
		month : number
	}

	type YearRange = { __$originalPosition?: UTSSourceMapPosition<"YearRange", "components/main-form/tools/main-yearmonth-picker.uvue", 67, 7>;
		before : number,
		after : number
	}

	const __sfc__ = defineComponent({
		name: "main-yearmonth-picker",
		emits: ['cancel', 'confirm'],
		props: {
			// 初始年份
			initialYear: {
				type: Number,
				default: () => new Date().getFullYear()
			},
			// 初始月份
			initialMonth: {
				type: Number,
				default: () => new Date().getMonth() + 1
			},
			before: {
				type: Number,
				default: () => 50
			},
			after: {
				type: Number,
				default: () => 10

			}
		},
		data() {
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 当前选中的年份
				selectedYear: new Date().getFullYear() as number,
				// 当前选中的月份
				selectedMonth: (new Date().getMonth() + 1) as number,
				// 年份列表
				yearList: [] as number[],
				// 月份列表
				monthList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as number[],
				// 年份滚动位置
				yearScrollTop: 0 as number
			}
		},
		created() {
			this.initializeData()
		},
		methods: {
			// 初始化数据
			initializeData() {
				this.selectedYear = this.initialYear
				this.selectedMonth = this.initialMonth
				this.generateYearList()
			},

			// 生成年份列表
			generateYearList() {
				const currentYear : number = new Date().getFullYear()


				const startYear : number = currentYear - this.before
				const endYear : number = currentYear + this.after

				this.yearList = []
				for (let year = startYear; year <= endYear; year++) {
					this.yearList.push(year)
				}
			},

			// 年份选择事件
			onYearSelect(year : number) {
				this.selectedYear = year
			},

			// 月份选择事件
			onMonthSelect(month : number) {
				this.selectedMonth = month
			},

			// 打开弹窗
			open() {
				this.visible = true
				this.calculateYearScrollPosition()
			},

			// 关闭弹窗
			close() {
				this.visible = false
			},

			// 计算年份滚动位置
			calculateYearScrollPosition() {
				// 在下一个tick中计算滚动位置，确保DOM已更新
				this.$nextTick(() => {
					const selectedIndex : number = this.yearList.indexOf(this.selectedYear)
					if (selectedIndex != -1) {
						// 每个年份项高度约为60rpx，让选中年份显示在中间位置
						this.yearScrollTop = Math.max(0, (selectedIndex - 2) * 30)
					}
				})
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.close()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.close()
				this.$emit('cancel')
			},

			// 确定按钮点击事件
			onConfirm() {
				this.close()
				const result : YearMonthData = {
					year: this.selectedYear,
					month: this.selectedMonth
				}
				this.$emit('confirm', result)
			}
		}
	})

export default __sfc__
function GenComponentsMainFormToolsMainYearmonthPickerRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
  return isTrue(_ctx.visible)
    ? _cE("view", _uM({
        key: 0,
        class: "picker-overlay",
        onClick: _ctx.onOverlayClick
      }), [
        _cE("view", _uM({
          class: "picker-modal",
          onClick: withModifiers(() => {}, ["stop"])
        }), [
          _cE("view", _uM({ class: "yearmonth-picker-container" }), [
            _cE("view", _uM({ class: "navbar" }), [
              _cE("text", _uM({
                class: "nav-btn cancel-btn",
                onClick: _ctx.onCancel
              }), "取消", 8 /* PROPS */, ["onClick"]),
              _cE("text", _uM({ class: "nav-title" }), "选择年月"),
              _cE("view", _uM({ class: "confirm-btn-container" }), [
                _cE("text", _uM({
                  class: "nav-btn confirm-btn",
                  onClick: _ctx.onConfirm
                }), "确定", 8 /* PROPS */, ["onClick"])
              ])
            ]),
            _cE("view", _uM({ class: "picker-body" }), [
              _cE("view", _uM({ class: "picker-section" }), [
                _cE("text", _uM({ class: "section-title" }), "年份"),
                _cE("scroll-view", _uM({
                  direction: "vertical",
                  class: "year-scroll",
                  "scroll-top": _ctx.yearScrollTop
                }), [
                  _cE("view", _uM({ class: "year-list" }), [
                    _cE(Fragment, null, RenderHelpers.renderList(_ctx.yearList, (year, __key, __index, _cached): any => {
                      return _cE("view", _uM({
                        key: year,
                        class: _nC(["year-item", _uM({ 'year-active': year == _ctx.selectedYear })]),
                        onClick: () => {_ctx.onYearSelect(year)}
                      }), [
                        _cE("text", _uM({
                          class: _nC(["year-text", _uM({ 'year-text-active': year == _ctx.selectedYear })])
                        }), _tD(year), 3 /* TEXT, CLASS */)
                      ], 10 /* CLASS, PROPS */, ["onClick"])
                    }), 128 /* KEYED_FRAGMENT */)
                  ])
                ], 8 /* PROPS */, ["scroll-top"])
              ]),
              _cE("view", _uM({ class: "picker-section picker-section-last" }), [
                _cE("text", _uM({ class: "section-title" }), "月份"),
                _cE("scroll-view", _uM({
                  direction: "vertical",
                  class: "month-scroll"
                }), [
                  _cE("view", _uM({ class: "month-grid" }), [
                    _cE(Fragment, null, RenderHelpers.renderList(_ctx.monthList, (month, __key, __index, _cached): any => {
                      return _cE("view", _uM({
                        key: month,
                        class: _nC(["month-item", _uM({ 'month-active': month == _ctx.selectedMonth })]),
                        onClick: () => {_ctx.onMonthSelect(month)}
                      }), [
                        _cE("text", _uM({
                          class: _nC(["month-text", _uM({ 'month-text-active': month == _ctx.selectedMonth })])
                        }), _tD(month) + "月", 3 /* TEXT, CLASS */)
                      ], 10 /* CLASS, PROPS */, ["onClick"])
                    }), 128 /* KEYED_FRAGMENT */)
                  ])
                ])
              ])
            ]),
            _cE("view", _uM({ class: "current-selection" }), [
              _cE("text", _uM({ class: "selection-label" }), "当前选择："),
              _cE("text", _uM({ class: "selection-value" }), _tD(_ctx.selectedYear) + "年" + _tD(_ctx.selectedMonth) + "月", 1 /* TEXT */)
            ])
          ])
        ], 8 /* PROPS */, ["onClick"])
      ], 8 /* PROPS */, ["onClick"])
    : _cC("v-if", true)
}
const GenComponentsMainFormToolsMainYearmonthPickerStyles = [_uM([["picker-overlay", _pS(_uM([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.5)"], ["display", "flex"], ["alignItems", "center"], ["justifyContent", "center"], ["zIndex", 1000]]))], ["picker-modal", _pS(_uM([["width", "90%"], ["maxWidth", "600rpx"], ["backgroundColor", "#ffffff"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["overflow", "hidden"], ["boxShadow", "0 8px 32px rgba(0, 0, 0, 0.3)"]]))], ["yearmonth-picker-container", _pS(_uM([["width", "100%"], ["backgroundColor", "#ffffff"], ["display", "flex"], ["flexDirection", "column"]]))], ["navbar", _pS(_uM([["height", 44], ["backgroundColor", "#f8f8f8"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#e5e5e5"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "space-between"], ["paddingTop", 0], ["paddingRight", 10], ["paddingBottom", 0], ["paddingLeft", 10]]))], ["nav-btn", _pS(_uM([["fontSize", 16], ["color", "#007aff"], ["paddingTop", 8], ["paddingRight", 12], ["paddingBottom", 8], ["paddingLeft", 12]]))], ["cancel-btn", _pS(_uM([["color", "#999999"]]))], ["confirm-btn-container", _pS(_uM([["height", 30], ["backgroundColor", "#007aff"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["boxShadow", "0 2rpx 8rpx rgba(0, 122, 255, 0.3)"]]))], ["confirm-btn", _pS(_uM([["color", "#ffffff"], ["fontWeight", "bold"]]))], ["nav-title", _pS(_uM([["fontSize", 17], ["color", "#333333"]]))], ["picker-body", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["height", "400rpx"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["picker-section", _pS(_uM([["flex", 1], ["display", "flex"], ["flexDirection", "column"], ["borderRightWidth", 1], ["borderRightStyle", "solid"], ["borderRightColor", "#f0f0f0"]]))], ["picker-section-last", _pS(_uM([["flex", 2], ["borderRightWidth", "medium"], ["borderRightStyle", "none"], ["borderRightColor", "#000000"]]))], ["section-title", _pS(_uM([["textAlign", "center"], ["paddingTop", "20rpx"], ["paddingRight", 0], ["paddingBottom", "20rpx"], ["paddingLeft", 0], ["fontSize", "28rpx"], ["fontWeight", "bold"], ["color", "#333333"], ["backgroundColor", "#f8f9fa"], ["borderBottomWidth", 1], ["borderBottomStyle", "solid"], ["borderBottomColor", "#f0f0f0"]]))], ["year-scroll", _pS(_uM([["flex", 1], ["height", "340rpx"]]))], ["year-list", _pS(_uM([["paddingTop", "10rpx"], ["paddingRight", 0], ["paddingBottom", "10rpx"], ["paddingLeft", 0]]))], ["year-item", _uM([["", _uM([["height", "60rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["marginTop", 0], ["marginRight", "15rpx"], ["marginBottom", "8rpx"], ["marginLeft", "15rpx"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"]])], [".year-active", _uM([["backgroundColor", "#007aff"], ["transform", "scale(1.05)"]])]])], ["year-text", _pS(_uM([["fontSize", "28rpx"], ["color", "#666666"]]))], ["year-text-active", _pS(_uM([["color", "#ffffff"], ["fontWeight", "bold"]]))], ["month-scroll", _pS(_uM([["flex", 1], ["height", "340rpx"]]))], ["month-grid", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["flexWrap", "wrap"], ["paddingTop", "20rpx"], ["paddingRight", "15rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "15rpx"], ["justifyContent", "space-between"], ["alignContent", "flex-start"], ["flex", 1]]))], ["month-item", _uM([["", _uM([["width", "100rpx"], ["height", "60rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["backgroundColor", "#f8f9fa"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["marginBottom", "12rpx"]])], [".month-active", _uM([["backgroundColor", "#007aff"], ["transform", "scale(1.05)"]])]])], ["month-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"]]))], ["month-text-active", _pS(_uM([["color", "#ffffff"], ["fontWeight", "bold"]]))], ["current-selection", _pS(_uM([["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"], ["backgroundColor", "#f8f9fa"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["justifyContent", "center"]]))], ["selection-label", _pS(_uM([["fontSize", "28rpx"], ["color", "#666666"], ["marginRight", "10rpx"]]))], ["selection-value", _pS(_uM([["fontSize", "32rpx"], ["color", "#007aff"], ["fontWeight", "bold"]]))]])]
