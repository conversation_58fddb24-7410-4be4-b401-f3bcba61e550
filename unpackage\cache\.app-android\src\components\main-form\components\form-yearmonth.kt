@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormYearmonth : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as String
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
                this.updateDisplayText()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_picker = resolveComponent("picker")
        val _component_form_container = resolveComponent("form-container")
        return _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
            return _uA(
                _cV(_component_picker, _uM("class" to "yearmonth-picker", "mode" to "date", "fields" to "month", "value" to _ctx.pickerValue, "start" to _ctx.startDate, "end" to _ctx.endDate, "onChange" to _ctx.onDateChange), _uM("default" to withSlotCtx(fun(): UTSArray<Any> {
                    return _uA(
                        _cE("view", _uM("class" to "yearmonth-display-container"), _uA(
                            _cE("view", _uM("class" to "yearmonth-icon"), _uA(
                                _cE("text", _uM("class" to "yearmonth-icon-text"), "📅")
                            )),
                            _cE("text", _uM("class" to "yearmonth-text"), _tD(_ctx.displayText), 1)
                        ))
                    )
                }
                ), "_" to 1), 8, _uA(
                    "value",
                    "start",
                    "end",
                    "onChange"
                ))
            )
        }
        ), "_" to 1), 8, _uA(
            "label",
            "show-error",
            "tip",
            "error-message",
            "label-color",
            "background-color"
        ))
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: String by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var displayText: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    open var pickerValue: String by `$data`
    open var startDate: String by `$data`
    open var endDate: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to "" as String, "isSave" to false, "save_key" to "", "tip" to "", "displayText" to "请选择年月", "showError" to false, "errorMessage" to "", "pickerValue" to "" as String, "startDate" to "1900-01" as String, "endDate" to "2100-12" as String)
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as String
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.tip = extalJson.getString("tip") ?: ""
        this.updateDisplayText()
        this.getCache()
    }
    open var updateDisplayText = ::gen_updateDisplayText_fn
    open fun gen_updateDisplayText_fn(): Unit {
        if (this.fieldValue != "" && this.fieldValue != null) {
            val yearMonthPattern = UTSRegExp("^\\d{4}-\\d{2}\$", "")
            if (yearMonthPattern.test(this.fieldValue)) {
                val parts = this.fieldValue.split("-")
                val year = parts[0]
                val month = parts[1]
                this.displayText = "" + year + "\u5E74" + month + "\u6708"
                this.pickerValue = "" + this.fieldValue + "-01"
            } else {
                this.displayText = this.fieldValue
                this.pickerValue = ""
            }
        } else {
            this.displayText = "请选择年月"
            this.pickerValue = ""
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data
                if (UTSAndroid.`typeof`(save_value) === "string") {
                    that.fieldValue = save_value as String
                    that.updateDisplayText()
                    val result = FormChangeEvent(index = this.index, value = save_value)
                    this.change(result)
                }
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave && UTSAndroid.`typeof`(this.fieldValue) === "string") {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.fieldValue == "" || this.fieldValue == null) {
            this.showError = true
            this.errorMessage = "请选择年月"
            return false
        }
        val yearMonthPattern = UTSRegExp("^\\d{4}-\\d{2}\$", "")
        if (!yearMonthPattern.test(this.fieldValue)) {
            this.showError = true
            this.errorMessage = "年月格式不正确"
            return false
        }
        val parts = this.fieldValue.split("-")
        val month = parseInt(parts[1])
        if (month < 1 || month > 12) {
            this.showError = true
            this.errorMessage = "月份必须在1-12之间"
            return false
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as String
        this.updateDisplayText()
        this.setCache()
        this.`$emit`("change", event)
    }
    open var onDateChange = ::gen_onDateChange_fn
    open fun gen_onDateChange_fn(event: UniPickerChangeEvent): Unit {
        val selectedDate = event.detail.value
        console.log("Selected date:", selectedDate, " at components/main-form/components/form-yearmonth.uvue:205")
    }
    companion object {
        var name = "FormYearmonth"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("yearmonth-picker" to _pS(_uM("flex" to 1)), "yearmonth-display-container" to _pS(_uM("flex" to 1, "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "minHeight" to "60rpx", "paddingTop" to "10rpx", "paddingRight" to "10rpx", "paddingBottom" to "10rpx", "paddingLeft" to "10rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "backgroundColor" to "rgba(255,255,255,0.8)")), "yearmonth-icon" to _pS(_uM("width" to "60rpx", "height" to "40rpx", "display" to "flex", "justifyContent" to "center", "alignItems" to "center", "marginRight" to "20rpx")), "yearmonth-icon-text" to _pS(_uM("fontSize" to "32rpx")), "yearmonth-text" to _pS(_uM("flex" to 1, "fontSize" to "28rpx", "color" to "#333333")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null)
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass)
    }
}
