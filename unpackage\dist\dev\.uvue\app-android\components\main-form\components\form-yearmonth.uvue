import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts';
import FormContainer from './form-container.uvue';
const __sfc__ = defineComponent({
    name: "FormYearmonth",
    emits: ['change'],
    components: {
        FormContainer
    },
    props: {
        data: {
            type: null as any as PropType<FormFieldData>
        },
        index: {
            type: Number,
            default: 0
        },
        keyName: {
            type: String,
            default: ""
        },
        labelColor: {
            type: String,
            default: "#000"
        },
        backgroundColor: {
            type: String,
            default: "#f1f4f9"
        }
    },
    data() {
        return {
            fieldName: "",
            fieldValue: "" as string,
            isSave: false,
            save_key: "",
            tip: "",
            displayText: "请选择年月",
            showError: false,
            errorMessage: "",
            pickerValue: "" as string,
            startDate: "1900-01" as string,
            endDate: "2100-12" as string
        };
    },
    computed: {},
    watch: {
        data: {
            handler(obj: FormFieldData) {
                // 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
                // 这避免了用户输入时的循环更新问题
                const newValue = obj.value as string;
                if (newValue !== this.fieldValue) {
                    this.fieldValue = newValue;
                    this.updateDisplayText();
                }
            },
            deep: true
        }
    },
    created(): void {
        // 初始化时调用一次即可
        const fieldObj = this.$props["data"] as FormFieldData;
        this.initFieldData(fieldObj);
    },
    methods: {
        // 初始化字段数据（仅在首次加载时调用）
        initFieldData(fieldObj: FormFieldData): void {
            const fieldKey = fieldObj.key;
            const fieldValue = fieldObj.value as string;
            // 设置基本信息
            this.fieldName = fieldObj.name;
            this.fieldValue = fieldValue;
            this.isSave = fieldObj.isSave ?? false;
            this.save_key = this.keyName + "_" + fieldKey;
            // 解析配置信息
            const extalJson = fieldObj.extra as UTSJSONObject;
            this.tip = extalJson.getString("tip") ?? "";
            // 更新显示文本
            this.updateDisplayText();
            // 获取缓存
            this.getCache();
        },
        // 更新显示文本
        updateDisplayText(): void {
            if (this.fieldValue != "" && this.fieldValue != null) {
                // 验证格式是否为 YYYY-MM
                const yearMonthPattern = /^\d{4}-\d{2}$/;
                if (yearMonthPattern.test(this.fieldValue)) {
                    const parts = this.fieldValue.split("-");
                    const year = parts[0];
                    const month = parts[1];
                    this.displayText = `${year}年${month}月`;
                    // 更新picker的值，picker需要完整的日期格式YYYY-MM-DD，这里设置为月份的第一天
                    this.pickerValue = `${this.fieldValue}-01`;
                }
                else {
                    this.displayText = this.fieldValue;
                    this.pickerValue = "";
                }
            }
            else {
                this.displayText = "请选择年月";
                this.pickerValue = "";
            }
        },
        getCache(): void {
            if (this.isSave) {
                const that = this;
                uni.getStorage({
                    key: this.save_key,
                    success: (res: GetStorageSuccess) => {
                        const save_value = res.data;
                        if (typeof save_value === 'string') {
                            that.fieldValue = save_value as string;
                            that.updateDisplayText();
                            const result: FormChangeEvent = {
                                index: this.index,
                                value: save_value
                            };
                            this.change(result);
                        }
                    }
                });
            }
        },
        setCache(): void {
            if (this.isSave && typeof this.fieldValue === "string") {
                uni.setStorage({
                    key: this.save_key,
                    data: this.fieldValue
                });
            }
        },
        validate(): boolean {
            // 年月值验证
            if (this.fieldValue == "" || this.fieldValue == null) {
                this.showError = true;
                this.errorMessage = "请选择年月";
                return false;
            }
            // 验证年月格式 YYYY-MM
            const yearMonthPattern = /^\d{4}-\d{2}$/;
            if (!yearMonthPattern.test(this.fieldValue)) {
                this.showError = true;
                this.errorMessage = "年月格式不正确";
                return false;
            }
            // 验证月份范围
            const parts = this.fieldValue.split("-");
            const month = parseInt(parts[1]);
            if (month < 1 || month > 12) {
                this.showError = true;
                this.errorMessage = "月份必须在1-12之间";
                return false;
            }
            this.showError = false;
            this.errorMessage = "";
            return true;
        },
        change(event: FormChangeEvent): void {
            // 更新字段值
            this.fieldValue = event.value as string;
            // 更新显示文本
            this.updateDisplayText();
            // 保存缓存
            this.setCache();
            // 触发父组件事件
            this.$emit('change', event);
        },
        // 日期选择器变化事件
        onDateChange(event: UniPickerChangeEvent): void {
            const selectedDate = event.detail.value;
            console.log("Selected date:", selectedDate, " at components/main-form/components/form-yearmonth.uvue:205");
            // // 从完整日期格式YYYY-MM-DD中提取年月部分YYYY-MM
            // if (selectedDate != "" && selectedDate != null) {
            // 	const yearMonth = selectedDate.substring(0, 7) // 提取YYYY-MM部分
            // 	const result: FormChangeEvent = {
            // 		index: this.index,
            // 		value: yearMonth
            // 	}
            // 	this.change(result)
            // }
        }
    }
});
export default __sfc__;
function GenComponentsMainFormComponentsFormYearmonthRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    const _component_picker = resolveComponent("picker");
    const _component_form_container = resolveComponent("form-container");
    return _cV(_component_form_container, _uM({
        label: _ctx.fieldName,
        "show-error": _ctx.showError,
        tip: _ctx.tip,
        "error-message": _ctx.errorMessage,
        "label-color": _ctx.labelColor,
        "background-color": _ctx.backgroundColor
    }), _uM({
        "input-content": withSlotCtx((): any[] => [
            _cV(_component_picker, _uM({
                class: "yearmonth-picker",
                mode: "date",
                fields: "month",
                value: _ctx.pickerValue,
                start: _ctx.startDate,
                end: _ctx.endDate,
                onChange: _ctx.onDateChange
            }), _uM({
                default: withSlotCtx((): any[] => [
                    _cE("view", _uM({ class: "yearmonth-display-container" }), [
                        _cE("view", _uM({ class: "yearmonth-icon" }), [
                            _cE("text", _uM({ class: "yearmonth-icon-text" }), "📅")
                        ]),
                        _cE("text", _uM({ class: "yearmonth-text" }), _tD(_ctx.displayText), 1 /* TEXT */)
                    ])
                ]),
                _: 1 /* STABLE */
            }), 8 /* PROPS */, ["value", "start", "end", "onChange"])
        ]),
        _: 1 /* STABLE */
    }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"]);
}
const GenComponentsMainFormComponentsFormYearmonthStyles = [_uM([["yearmonth-picker", _pS(_uM([["flex", 1]]))], ["yearmonth-display-container", _pS(_uM([["flex", 1], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["minHeight", "60rpx"], ["paddingTop", "10rpx"], ["paddingRight", "10rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "10rpx"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["backgroundColor", "rgba(255,255,255,0.8)"]]))], ["yearmonth-icon", _pS(_uM([["width", "60rpx"], ["height", "40rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["marginRight", "20rpx"]]))], ["yearmonth-icon-text", _pS(_uM([["fontSize", "32rpx"]]))], ["yearmonth-text", _pS(_uM([["flex", 1], ["fontSize", "28rpx"], ["color", "#333333"]]))]])];
//# sourceMappingURL=form-yearmonth.uvue.map