<template>
	<view class="example-container">
		<text class="example-title">年月选择器示例</text>
		
		<!-- 显示当前选择的年月 -->
		<view class="current-display">
			<text class="display-label">当前选择：</text>
			<text class="display-value">{{ currentYear }}年{{ currentMonth }}月</text>
		</view>
		
		<!-- 打开选择器按钮 -->
		<view class="open-button" @click="openPicker">
			<text class="button-text">选择年月</text>
		</view>
		
		<!-- 年月选择器组件 -->
		<main-yearmonth-picker 
			ref="yearmonthPicker"
			:initial-year="currentYear"
			:initial-month="currentMonth"
			:year-range="{ before: 30, after: 5 }"
			@cancel="onPickerCancel"
			@confirm="onPickerConfirm"
		></main-yearmonth-picker>
	</view>
</template>

<script>
	// 定义年月数据类型
	type YearMonthResult = {
		year: number,
		month: number
	}

	export default {
		name: "main-yearmonth-picker-example",
		data() {
			return {
				// 当前选择的年份
				currentYear: new Date().getFullYear() as number,
				// 当前选择的月份
				currentMonth: (new Date().getMonth() + 1) as number
			}
		},
		methods: {
			// 打开年月选择器
			openPicker() {
				const picker = this.$refs['yearmonthPicker'] as ComponentPublicInstance
				if (picker != null) {
					picker.$callMethod('open')
				}
			},

			// 选择器取消事件
			onPickerCancel() {
				console.log('用户取消了选择')
			},

			// 选择器确认事件
			onPickerConfirm(result: YearMonthResult) {
				console.log('用户选择了：', result)
				this.currentYear = result.year
				this.currentMonth = result.month
				
				// 可以在这里执行其他业务逻辑
				uni.showToast({
					title: `已选择 ${result.year}年${result.month}月`,
					icon: 'success'
				})
			}
		}
	}
</script>

<style>
	.example-container {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.example-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 40rpx;
	}

	.current-display {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-bottom: 40rpx;
		padding: 20rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
	}

	.display-label {
		font-size: 28rpx;
		color: #666666;
		margin-right: 10rpx;
	}

	.display-value {
		font-size: 32rpx;
		color: #007aff;
		font-weight: bold;
	}

	.open-button {
		width: 200rpx;
		height: 80rpx;
		background-color: #007aff;
		border-radius: 12rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
	}

	.button-text {
		font-size: 28rpx;
		color: #ffffff;
		font-weight: bold;
	}
</style>
