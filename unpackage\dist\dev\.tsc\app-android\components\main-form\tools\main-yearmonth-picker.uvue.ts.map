{"version": 3, "file": "components/main-form/tools/main-yearmonth-picker.uvue", "names": [], "sources": ["components/main-form/tools/main-yearmonth-picker.uvue"], "sourcesContent": ["<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"yearmonth-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">选择年月</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 年月选择区域 -->\r\n\t\t\t\t<view class=\"picker-body\">\r\n\t\t\t\t\t<!-- 年份选择区域 -->\r\n\t\t\t\t\t<view class=\"picker-section\">\r\n\t\t\t\t\t\t<text class=\"section-title\">年份</text>\r\n\r\n\t\t\t\t\t\t<scroll-view direction=\"vertical\" class=\"year-scroll\" :scroll-top=\"yearScrollTop\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"year-list\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"year in yearList\" :key=\"year\" class=\"year-item\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'year-active': year == selectedYear }\" @click=\"onYearSelect(year)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"year-text\" :class=\"{ 'year-text-active': year == selectedYear }\">{{ year }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 月份选择区域 -->\r\n\t\t\t\t\t<view class=\"picker-section picker-section-last\">\r\n\t\t\t\t\t\t<text class=\"section-title\">月份</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t<scroll-view direction=\"vertical\" class=\"month-scroll\">\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t\t<view class=\"month-grid\">\r\n\t\t\t\t\t\t\t\t<view v-for=\"month in monthList\" :key=\"month\" class=\"month-item\"\r\n\t\t\t\t\t\t\t\t\t:class=\"{ 'month-active': month == selectedMonth }\" @click=\"onMonthSelect(month)\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"month-text\" :class=\"{ 'month-text-active': month == selectedMonth }\">{{ month }}月</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 当前选择显示区域 -->\r\n\t\t\t\t<view class=\"current-selection\">\r\n\t\t\t\t\t<text class=\"selection-label\">当前选择：</text>\r\n\t\t\t\t\t<text class=\"selection-value\">{{ selectedYear }}年{{ selectedMonth }}月</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义年月选择器的数据类型\r\n\ttype YearMonthData = {\r\n\t\tyear : number,\r\n\t\tmonth : number\r\n\t}\r\n\r\n\ttype YearRange = {\r\n\t\tbefore : number,\r\n\t\tafter : number\r\n\t}\r\n\r\n\texport default {\r\n\t\tname: \"main-yearmonth-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tprops: {\r\n\t\t\t// 初始年份\r\n\t\t\tinitialYear: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => new Date().getFullYear()\r\n\t\t\t},\r\n\t\t\t// 初始月份\r\n\t\t\tinitialMonth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => new Date().getMonth() + 1\r\n\t\t\t},\r\n\t\t\tbefore: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => 50\r\n\t\t\t},\r\n\t\t\tafter: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: () => 10\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的年份\r\n\t\t\t\tselectedYear: new Date().getFullYear() as number,\r\n\t\t\t\t// 当前选中的月份\r\n\t\t\t\tselectedMonth: (new Date().getMonth() + 1) as number,\r\n\t\t\t\t// 年份列表\r\n\t\t\t\tyearList: [] as number[],\r\n\t\t\t\t// 月份列表\r\n\t\t\t\tmonthList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as number[],\r\n\t\t\t\t// 年份滚动位置\r\n\t\t\t\tyearScrollTop: 0 as number\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.initializeData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 初始化数据\r\n\t\t\tinitializeData() {\r\n\t\t\t\tthis.selectedYear = this.initialYear\r\n\t\t\t\tthis.selectedMonth = this.initialMonth\r\n\t\t\t\tthis.generateYearList()\r\n\t\t\t},\r\n\r\n\t\t\t// 生成年份列表\r\n\t\t\tgenerateYearList() {\r\n\t\t\t\tconst currentYear : number = new Date().getFullYear()\r\n\r\n\r\n\t\t\t\tconst startYear : number = currentYear - this.before\r\n\t\t\t\tconst endYear : number = currentYear + this.after\r\n\r\n\t\t\t\tthis.yearList = []\r\n\t\t\t\tfor (let year = startYear; year <= endYear; year++) {\r\n\t\t\t\t\tthis.yearList.push(year)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 年份选择事件\r\n\t\t\tonYearSelect(year : number) {\r\n\t\t\t\tthis.selectedYear = year\r\n\t\t\t},\r\n\r\n\t\t\t// 月份选择事件\r\n\t\t\tonMonthSelect(month : number) {\r\n\t\t\t\tthis.selectedMonth = month\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t\tthis.calculateYearScrollPosition()\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 计算年份滚动位置\r\n\t\t\tcalculateYearScrollPosition() {\r\n\t\t\t\t// 在下一个tick中计算滚动位置，确保DOM已更新\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst selectedIndex : number = this.yearList.indexOf(this.selectedYear)\r\n\t\t\t\t\tif (selectedIndex != -1) {\r\n\t\t\t\t\t\t// 每个年份项高度约为60rpx，让选中年份显示在中间位置\r\n\t\t\t\t\t\tthis.yearScrollTop = Math.max(0, (selectedIndex - 2) * 30)\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst result : YearMonthData = {\r\n\t\t\t\t\tyear: this.selectedYear,\r\n\t\t\t\t\tmonth: this.selectedMonth\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('confirm', result)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 90%;\r\n\t\tmax-width: 600rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.yearmonth-picker-container {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 选择器主体区域 */\r\n\t.picker-body {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\theight: 400rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.picker-section {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tborder-right: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.picker-section-last {\r\n\t\tflex:2;\r\n\t\tborder-right: none;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t/* 年份选择区域 */\r\n\t.year-scroll {\r\n\t\tflex: 1;\r\n\t\theight: 340rpx;\r\n\t}\r\n\r\n\t.year-list {\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.year-item {\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin: 0 15rpx 8rpx 15rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.year-item.year-active {\r\n\t\tbackground-color: #007aff;\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.year-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.year-text-active {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 月份选择区域 */\r\n\t.month-scroll {\r\n\t\tflex: 1;\r\n\t\theight: 340rpx;\r\n\t}\r\n\r\n\t.month-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tpadding: 20rpx 15rpx;\r\n\t\tjustify-content: space-between;\r\n\t\talign-content: flex-start;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.month-item {\r\n\t\twidth: 100rpx;\r\n\t\theight: 60rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.month-item.month-active {\r\n\t\tbackground-color: #007aff;\r\n\t\ttransform: scale(1.05);\r\n\t}\r\n\r\n\t.month-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.month-text-active {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* 当前选择显示区域 */\r\n\t.current-selection {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.selection-label {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.selection-value {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #007aff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>"], "mappings": ";CA4DC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CACd,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EACpB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACd;;CAEA,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACf,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;CACd;;CAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACd,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACZ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACvC,CAAC;GACD,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACb,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;GACxC,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;GACjB,CAAC;GACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;;GAEjB;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GAC1B;EACD,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;GACR,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;GACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACvB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAClB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;;IAGpD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACjB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;KACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;GACD,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;GACxB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;GACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;GAC1B,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAClC,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC;GACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;KACpB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;MACxB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;KAC1D;IACD,CAAC;GACF,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACpB,CAAC;;GAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE;KAC9B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACvB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzB;IACA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;GAC7B;EACD;CACD;;;;;;gBA7LY,YAAO;MAAnB,IAsDO;;QAtDc,KAAK,EAAC,gBAAgB;QAAE,OAAK,EAAE,mBAAc;;QACjE,IAoDO;UApDD,KAAK,EAAC,cAAc;UAAE,OAAK,gBAAN,QAAc;;UACxC,IAkDO,cAlDD,KAAK,EAAC,4BAA4B;YAEvC,IAMO,cAND,KAAK,EAAC,QAAQ;cACnB,IAA4D;gBAAtD,KAAK,EAAC,oBAAoB;gBAAE,OAAK,EAAE,aAAQ;kBAAE,IAAE;cACrD,IAAmC,cAA7B,KAAK,EAAC,WAAW,KAAC,MAAI;cAC5B,IAEO,cAFD,KAAK,EAAC,uBAAuB;gBAClC,IAA8D;kBAAxD,KAAK,EAAC,qBAAqB;kBAAE,OAAK,EAAE,cAAS;oBAAE,IAAE;;;YAKzD,IAgCO,cAhCD,KAAK,EAAC,aAAa;cAExB,IAaO,cAbD,KAAK,EAAC,gBAAgB;gBAC3B,IAAqC,cAA/B,KAAK,EAAC,eAAe,KAAC,IAAE;gBAE9B,IASc;kBATD,SAAS,EAAC,UAAU;kBAAC,KAAK,EAAC,aAAa;kBAAE,YAAU,EAAE,kBAAa;;kBAE/E,IAKO,cALD,KAAK,EAAC,WAAW;oBACtB,IAGO,yCAHc,aAAQ,GAAhB,IAAI,EAAJ,KAAI,EAAJ,OAAI;6BAAjB,IAGO;wBAHyB,GAAG,EAAE,IAAI;wBAAE,KAAK,OAAC,WAAW,EACnD,iDAAuC;wBAAG,OAAK,SAAE,iBAAY,CAAC,IAAI;;wBAC1E,IAA+F;0BAAzF,KAAK,OAAC,WAAW,EAAS,sDAA4C;gCAAK,IAAI;;;;;;cAQzF,IAaO,cAbD,KAAK,EAAC,oCAAoC;gBAC/C,IAAqC,cAA/B,KAAK,EAAC,eAAe,KAAC,IAAE;gBAE9B,IASc;kBATD,SAAS,EAAC,UAAU;kBAAC,KAAK,EAAC,cAAc;;kBAErD,IAKO,cALD,KAAK,EAAC,YAAY;oBACvB,IAGO,yCAHe,cAAS,GAAlB,KAAK,EAAL,KAAK,EAAL,OAAK;6BAAlB,IAGO;wBAH2B,GAAG,EAAE,KAAK;wBAAE,KAAK,OAAC,YAAY,EACvD,oDAA0C;wBAAG,OAAK,SAAE,kBAAa,CAAC,KAAK;;wBAC/E,IAAqG;0BAA/F,KAAK,OAAC,YAAY,EAAS,yDAA+C;gCAAK,KAAK,IAAG,GAAC;;;;;;;YASnG,IAGO,cAHD,KAAK,EAAC,mBAAmB;cAC9B,IAA0C,cAApC,KAAK,EAAC,iBAAiB,KAAC,OAAK;cACnC,IAA4E,cAAtE,KAAK,EAAC,iBAAiB,SAAI,iBAAY,IAAG,GAAC,OAAG,kBAAa,IAAG,GAAC"}